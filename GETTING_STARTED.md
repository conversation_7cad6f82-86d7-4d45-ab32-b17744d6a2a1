# Getting Started with vite-plugin-design-tokens

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 构建插件

```bash
npm run build
```

### 3. 运行示例

```bash
npm run dev:example
```

这将启动一个示例项目，展示插件的各种功能。

## 配置 Figma 集成

### 1. 获取 Figma Access Token

1. 登录 [Figma](https://figma.com)
2. 进入 Settings > Account > Personal access tokens
3. 生成一个新的 token

### 2. 获取 Figma File ID

从 Figma 文件 URL 中提取文件 ID：
```
https://www.figma.com/file/[FILE_ID]/[FILE_NAME]
```

### 3. 配置环境变量

复制 `.env.example` 为 `.env`：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
FIGMA_ACCESS_TOKEN=your_actual_token_here
FIGMA_FILE_ID=your_actual_file_id_here
```

### 4. 更新 Vite 配置

在 `example/vite.config.ts` 中更新配置：

```typescript
figma: {
  fileId: process.env.FIGMA_FILE_ID || '',
  token: process.env.FIGMA_ACCESS_TOKEN || '',
  page: 'Design Tokens', // 可选：指定页面名称
  pollingInterval: 30000
}
```

## 使用本地 JSON 文件

如果不使用 Figma，可以直接使用本地 JSON 文件：

1. 编辑 `example/tokens.json`
2. 按照 W3C 设计令牌格式添加你的令牌
3. 插件会自动监听文件变化并重新生成输出

## 输出格式

插件支持多种输出格式：

### CSS 变量
```css
:root {
  --dt-color-primary-base: #0066ff;
  --dt-spacing-md: 16px;
}
```

### TypeScript 对象
```typescript
export const Tokens = {
  color: {
    primary: {
      base: '#0066ff'
    }
  }
} as const;
```

### Tailwind 配置
```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          base: '#0066ff'
        }
      }
    }
  }
}
```

## 开发工作流

1. **设计阶段**：在 Figma 中创建和管理设计令牌
2. **开发阶段**：插件自动同步令牌到代码中
3. **使用阶段**：在 CSS/JS 中使用生成的令牌
4. **更新阶段**：设计更改时自动热重载

## 自定义转换器

你可以创建自定义转换器来处理特定的令牌类型：

```typescript
transformers: [
  {
    name: 'color-rgb',
    transform: (token) => {
      if (token.$type === 'color') {
        return {
          ...token,
          $extensions: {
            rgb: hexToRgb(token.$value)
          }
        }
      }
      return token
    }
  }
]
```

## 故障排除

### 常见问题

1. **Figma API 错误**
   - 检查 token 是否有效
   - 确认文件 ID 正确
   - 验证网络连接

2. **文件生成失败**
   - 检查输出路径权限
   - 确认目录存在

3. **热重载不工作**
   - 检查文件监听配置
   - 确认 Vite 开发服务器运行正常

### 调试模式

启用调试模式获取详细日志：

```typescript
designTokens({
  debug: true
})
```

## 下一步

- 查看 [完整文档](./redeme.md)
- 探索 [示例项目](./example/)
- 运行 [测试](./tests/)
- 贡献代码或报告问题
