# vite-plugin-design-tokens

A Vite plugin for seamlessly integrating design tokens from Figma and other sources into your development workflow.

## Features

- 🔌 **Seamless Integration**: Deep integration with Vite build tool
- 🎨 **Multiple Data Sources**: Support for Figma API and local design token files
- 📦 **Multiple Output Formats**: Generate CSS variables, TypeScript/JavaScript objects, Tailwind config, etc.
- 🛡 **Type Safety**: Automatic TypeScript type definition generation
- 🔥 **Hot Reload Support**: Automatic updates when design changes
- ⚙ **Highly Configurable**: Support for custom output formats and transformation rules
- 📊 **Visual Debugging**: Optional token documentation page generation

## Quick Start

### Installation

```bash
npm install vite-plugin-design-tokens --save-dev
```

### Basic Configuration

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import designTokens from 'vite-plugin-design-tokens'

export default defineConfig({
  plugins: [
    designTokens({
      // Use local JSON file
      json: './tokens.json',
      
      // Generate CSS variables
      output: {
        css: {
          path: './src/styles/tokens.css',
          prefix: 'dt'
        }
      }
    })
  ]
})
```

### With Figma Integration

```typescript
designTokens({
  figma: {
    fileId: 'your-figma-file-id',
    token: process.env.FIGMA_ACCESS_TOKEN,
    pollingInterval: 30000
  },
  output: {
    css: './src/styles/tokens.css',
    ts: './src/tokens.ts',
    tailwind: './tailwind.tokens.js'
  }
})
```

## Development

```bash
# Install dependencies
npm install

# Run example
cd example
npm install
npm run dev

# Build plugin
npm run build

# Run tests
npm test
```

## Documentation

For detailed documentation, see [redeme.md](./redeme.md).

## License

MIT
