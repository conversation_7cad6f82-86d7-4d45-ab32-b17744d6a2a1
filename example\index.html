<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Design Tokens Example</title>
  <link rel="stylesheet" href="./src/styles/tokens.css">
  <link rel="stylesheet" href="./src/styles/main.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>Design Tokens Example</h1>
      <p>This example demonstrates the vite-plugin-design-tokens in action.</p>
    </header>

    <main>
      <section class="color-section">
        <h2>Colors</h2>
        <div class="color-grid">
          <div class="color-item primary">
            <div class="color-swatch"></div>
            <span>Primary</span>
          </div>
          <div class="color-item secondary">
            <div class="color-swatch"></div>
            <span>Secondary</span>
          </div>
          <div class="color-item success">
            <div class="color-swatch"></div>
            <span>Success</span>
          </div>
          <div class="color-item warning">
            <div class="color-swatch"></div>
            <span>Warning</span>
          </div>
          <div class="color-item error">
            <div class="color-swatch"></div>
            <span>Error</span>
          </div>
        </div>
      </section>

      <section class="typography-section">
        <h2>Typography</h2>
        <div class="typography-examples">
          <h1 class="heading-h1">Heading 1 Example</h1>
          <h2 class="heading-h2">Heading 2 Example</h2>
          <p class="body-regular">This is regular body text using design tokens.</p>
          <p class="body-small">This is small body text using design tokens.</p>
        </div>
      </section>

      <section class="spacing-section">
        <h2>Spacing</h2>
        <div class="spacing-examples">
          <div class="spacing-item xs">XS</div>
          <div class="spacing-item sm">SM</div>
          <div class="spacing-item md">MD</div>
          <div class="spacing-item lg">LG</div>
          <div class="spacing-item xl">XL</div>
        </div>
      </section>

      <section class="shadow-section">
        <h2>Shadows</h2>
        <div class="shadow-examples">
          <div class="shadow-item sm">Small Shadow</div>
          <div class="shadow-item md">Medium Shadow</div>
          <div class="shadow-item lg">Large Shadow</div>
        </div>
      </section>
    </main>

    <footer>
      <p>
        <button id="theme-toggle">Toggle Dark Theme</button>
        <a href="/__design-tokens" target="_blank">View Token Documentation</a>
      </p>
    </footer>
  </div>

  <script type="module" src="./src/main.ts"></script>
</body>
</html>
