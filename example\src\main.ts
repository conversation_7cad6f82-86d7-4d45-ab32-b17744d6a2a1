// 导入生成的设计令牌
import { Tokens } from './tokens'

// 主题切换功能
function setupThemeToggle() {
  const toggleButton = document.getElementById('theme-toggle')
  const body = document.body
  
  if (!toggleButton) return

  // 检查本地存储的主题设置
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark') {
    body.setAttribute('data-theme', 'dark')
    toggleButton.textContent = 'Switch to Light Theme'
  }

  toggleButton.addEventListener('click', () => {
    const currentTheme = body.getAttribute('data-theme')
    
    if (currentTheme === 'dark') {
      body.removeAttribute('data-theme')
      toggleButton.textContent = 'Toggle Dark Theme'
      localStorage.setItem('theme', 'light')
    } else {
      body.setAttribute('data-theme', 'dark')
      toggleButton.textContent = 'Switch to Light Theme'
      localStorage.setItem('theme', 'dark')
    }
  })
}

// 显示令牌信息
function displayTokenInfo() {
  console.log('Design Tokens loaded:', Tokens)
  
  // 在控制台中显示可用的令牌
  console.group('Available Design Tokens:')
  console.log('Colors:', Tokens.color)
  console.log('Spacing:', Tokens.spacing)
  console.log('Typography:', Tokens.typography)
  console.log('Shadows:', Tokens.shadow)
  console.groupEnd()
}

// 初始化应用
function init() {
  setupThemeToggle()
  displayTokenInfo()
  
  console.log('Design tokens example initialized')
}

// 当 DOM 加载完成时初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init)
} else {
  init()
}
