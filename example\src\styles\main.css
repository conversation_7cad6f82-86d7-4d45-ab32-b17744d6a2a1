/* 使用设计令牌的样式 */

body {
  margin: 0;
  padding: 0;
  font-family: var(--dt-typography-body-regular-fontFamily, 'Inter', sans-serif);
  background-color: #ffffff;
  color: #333333;
  transition: background-color 0.3s ease, color 0.3s ease;
}

[data-theme="dark"] body {
  background-color: #1a1a1a;
  color: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--dt-spacing-lg, 24px);
}

header {
  text-align: center;
  margin-bottom: var(--dt-spacing-xl, 32px);
  padding-bottom: var(--dt-spacing-lg, 24px);
  border-bottom: 1px solid #e9ecef;
}

header h1 {
  color: var(--dt-color-primary-base, #0066ff);
  margin-bottom: var(--dt-spacing-sm, 8px);
}

section {
  margin-bottom: var(--dt-spacing-xl, 32px);
}

h2 {
  margin-bottom: var(--dt-spacing-md, 16px);
  color: var(--dt-color-secondary-base, #6c757d);
}

/* 颜色示例 */
.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--dt-spacing-md, 16px);
}

.color-item {
  text-align: center;
}

.color-swatch {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  margin-bottom: var(--dt-spacing-sm, 8px);
  border: 1px solid #e9ecef;
}

.color-item.primary .color-swatch {
  background-color: var(--dt-color-primary-base, #0066ff);
}

.color-item.secondary .color-swatch {
  background-color: var(--dt-color-secondary-base, #6c757d);
}

.color-item.success .color-swatch {
  background-color: var(--dt-color-success-base, #28a745);
}

.color-item.warning .color-swatch {
  background-color: var(--dt-color-warning-base, #ffc107);
}

.color-item.error .color-swatch {
  background-color: var(--dt-color-error-base, #dc3545);
}

/* 排版示例 */
.typography-examples > * {
  margin-bottom: var(--dt-spacing-md, 16px);
}

.heading-h1 {
  font-size: var(--dt-typography-heading-h1-fontSize, 32px);
  font-weight: var(--dt-typography-heading-h1-fontWeight, 700);
  line-height: var(--dt-typography-heading-h1-lineHeight, 40px);
}

.heading-h2 {
  font-size: var(--dt-typography-heading-h2-fontSize, 24px);
  font-weight: var(--dt-typography-heading-h2-fontWeight, 600);
  line-height: var(--dt-typography-heading-h2-lineHeight, 32px);
}

.body-regular {
  font-size: var(--dt-typography-body-regular-fontSize, 16px);
  font-weight: var(--dt-typography-body-regular-fontWeight, 400);
  line-height: var(--dt-typography-body-regular-lineHeight, 24px);
}

.body-small {
  font-size: var(--dt-typography-body-small-fontSize, 14px);
  font-weight: var(--dt-typography-body-small-fontWeight, 400);
  line-height: var(--dt-typography-body-small-lineHeight, 20px);
}

/* 间距示例 */
.spacing-examples {
  display: flex;
  gap: var(--dt-spacing-md, 16px);
  flex-wrap: wrap;
}

.spacing-item {
  background: var(--dt-color-primary-light, #3399ff);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-weight: 600;
}

.spacing-item.xs {
  padding: var(--dt-spacing-xs, 4px);
}

.spacing-item.sm {
  padding: var(--dt-spacing-sm, 8px);
}

.spacing-item.md {
  padding: var(--dt-spacing-md, 16px);
}

.spacing-item.lg {
  padding: var(--dt-spacing-lg, 24px);
}

.spacing-item.xl {
  padding: var(--dt-spacing-xl, 32px);
}

/* 阴影示例 */
.shadow-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--dt-spacing-lg, 24px);
}

.shadow-item {
  background: white;
  padding: var(--dt-spacing-lg, 24px);
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
}

.shadow-item.sm {
  box-shadow: var(--dt-shadow-sm, 0 1px 2px rgba(0, 0, 0, 0.05));
}

.shadow-item.md {
  box-shadow: var(--dt-shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.shadow-item.lg {
  box-shadow: var(--dt-shadow-lg, 0 10px 15px rgba(0, 0, 0, 0.1));
}

/* 页脚 */
footer {
  text-align: center;
  margin-top: var(--dt-spacing-xl, 32px);
  padding-top: var(--dt-spacing-lg, 24px);
  border-top: 1px solid #e9ecef;
}

footer button, footer a {
  margin: 0 var(--dt-spacing-sm, 8px);
  padding: var(--dt-spacing-sm, 8px) var(--dt-spacing-md, 16px);
  border: 1px solid var(--dt-color-primary-base, #0066ff);
  border-radius: 4px;
  text-decoration: none;
  color: var(--dt-color-primary-base, #0066ff);
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

footer button:hover, footer a:hover {
  background: var(--dt-color-primary-base, #0066ff);
  color: white;
}
