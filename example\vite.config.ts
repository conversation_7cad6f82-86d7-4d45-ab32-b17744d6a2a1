import { defineConfig } from 'vite'
import designTokens from '../src/index'

export default defineConfig({
  plugins: [
    designTokens({
      // Figma 配置
      figma: {
        fileId: 'your-figma-file-id',
        token: process.env.FIGMA_ACCESS_TOKEN || '',
        page: 'Design Tokens',
        pollingInterval: 30000
      },
      
      // 本地 JSON 备用
      json: {
        path: './tokens.json',
        watch: true
      },
      
      // 输出配置
      output: {
        css: {
          path: './src/styles/tokens.css',
          selector: ':root',
          prefix: 'dt',
          darkThemeSelector: '[data-theme="dark"]'
        },
        ts: {
          path: './src/tokens.ts',
          format: 'object',
          typeDeclaration: true
        },
        tailwind: {
          path: './tailwind.tokens.js',
          extend: true
        },
        json: {
          path: './dist/tokens.json',
          minify: false
        }
      },
      
      // 自定义转换器
      transformers: [
        {
          name: 'color-rgb',
          transform: (token) => {
            if (token.$type === 'color' && typeof token.$value === 'string') {
              return {
                ...token,
                $extensions: {
                  ...token.$extensions,
                  rgb: hexToRgb(token.$value)
                }
              }
            }
            return token
          }
        }
      ],
      
      // 文档生成
      documentation: {
        path: './design-tokens-docs.html',
        title: '项目设计令牌文档',
        open: process.env.NODE_ENV === 'development'
      },
      
      // 调试模式
      debug: process.env.NODE_ENV === 'development'
    })
  ]
})

// 辅助函数
function hexToRgb(hex: string): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  if (!result) return hex
  
  const r = parseInt(result[1], 16)
  const g = parseInt(result[2], 16)
  const b = parseInt(result[3], 16)
  
  return `rgb(${r}, ${g}, ${b})`
}
