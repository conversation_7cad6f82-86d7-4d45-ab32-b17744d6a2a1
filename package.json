{"name": "vite-plugin-design-tokens", "version": "1.0.0", "description": "A Vite plugin for integrating design tokens from Figma and other sources", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "dev:example": "node scripts/dev.js", "test": "vitest", "test:ui": "vitest --ui", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "prepublishOnly": "npm run build"}, "keywords": ["vite", "plugin", "design-tokens", "figma", "css-variables", "typescript", "tailwind"], "author": "Your Name", "license": "MIT", "peerDependencies": {"vite": "^4.0.0 || ^5.0.0"}, "dependencies": {"chokidar": "^3.5.3", "picocolors": "^1.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "tsup": "^8.0.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vitest": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/vite-plugin-design-tokens.git"}, "bugs": {"url": "https://github.com/your-org/vite-plugin-design-tokens/issues"}, "homepage": "https://github.com/your-org/vite-plugin-design-tokens#readme"}