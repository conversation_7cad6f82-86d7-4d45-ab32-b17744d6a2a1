# vite-plugin-design-tokens 设计文档

## 概述

`vite-plugin-design-tokens` 是一个旨在打通设计与开发工作流的 Vite 插件。它自动从设计工具（如 Figma）或设计令牌文件中提取设计令牌，并将其转换为开发可用的格式（CSS 变量、TypeScript 类型、Tailwind 配置等），确保设计系统的一致性并提升团队协作效率。

## 功能特性

- 🔌 **无缝集成**：与 Vite 构建工具深度集成
- 🎨 **多数据源支持**：支持 Figma API 和本地设计令牌文件
- 📦 **多格式输出**：生成 CSS 变量、TypeScript/JavaScript 对象、Tailwind 配置等
- 🛡 **类型安全**：自动生成 TypeScript 类型定义
- 🔥 **热重载支持**：设计变更时自动更新开发环境
- ⚙ **高度可配置**：支持自定义输出格式和转换规则
- 📊 **可视化调试**：可选生成令牌文档页面

## 安装

```bash
npm install vite-plugin-design-tokens --save-dev
# 或
yarn add vite-plugin-design-tokens --dev
# 或
pnpm add vite-plugin-design-tokens --save-dev
```

## 基本配置

```javascript
// vite.config.js / vite.config.ts
import { defineConfig } from 'vite'
import designTokens from 'vite-plugin-design-tokens'

export default defineConfig({
  plugins: [
    designTokens({
      // 插件配置选项
    })
  ]
})
```

## 配置选项

### 数据源配置

#### figma

- **类型**: `Object`
- **可选**: 是
- **说明**: 从 Figma 文件获取设计令牌的配置

```javascript
figma: {
  fileId: 'your-figma-file-id', // Figma 文件ID
  token: process.env.FIGMA_TOKEN, // Figma Personal Access Token
  page: 'Design Tokens', // 可选：指定页面名称
  pollingInterval: 30000, // 可选：轮询间隔(ms)，默认30000
  headers: { // 可选：自定义请求头
    'X-Custom-Header': 'value'
  }
}
```

#### json

- **类型**: `Object | String`
- **可选**: 是
- **说明**: 从本地 JSON 文件获取设计令牌

```javascript
json: {
  path: './tokens.json', // JSON文件路径
  watch: true // 是否监听文件变化
}

// 或简写为字符串
json: './tokens.json'
```

### 输出配置

#### output.css

- **类型**: `Object | Boolean`
- **可选**: 是
- **默认**: `false`
- **说明**: 生成 CSS 变量文件配置

```javascript
css: {
  path: './src/styles/tokens.css', // 输出路径
  selector: ':root', // CSS选择器，默认:root
  prefix: 'td', // 可选：变量前缀
  darkThemeSelector: '[data-theme="dark"]' // 可选：暗色主题选择器
}
```

#### output.ts

- **类型**: `Object | Boolean`
- **可选**: 是
- **默认**: `false`
- **说明**: 生成 TypeScript/JavaScript 文件配置

```javascript
ts: {
  path: './src/utils/tokens.ts', // 输出路径
  format: 'object', // 输出格式: 'object' | 'constants'
  typeDeclaration: true // 是否生成类型声明
}
```

#### output.tailwind

- **类型**: `Object | Boolean`
- **可选**: 是
- **默认**: `false`
- **说明**: 生成 Tailwind CSS 配置

```javascript
tailwind: {
  path: './tailwind.tokens.js', // 输出路径
  extend: true // 是否生成extend配置
}
```

#### output.json

- **类型**: `Object | Boolean`
- **可选**: 是
- **默认**: `false`
- **说明**: 生成处理后的 JSON 文件

```javascript
json: {
  path: './src/tokens/output.json', // 输出路径
  minify: false // 是否压缩JSON
}
```

### 转换器配置

#### transformers

- **类型**: `Array`
- **可选**: 是
- **说明**: 自定义令牌转换器

```javascript
transformers: [
  {
    name: 'custom-transformer',
    transform: (token, context) => {
      // 自定义转换逻辑
      return transformedToken
    }
  }
]
```

### 其他配置

#### watch

- **类型**: `Boolean`
- **可选**: 是
- **默认**: `true`
- **说明**: 是否监听令牌源变化

#### debug

- **类型**: `Boolean`
- **可选**: 是
- **默认**: `false`
- **说明**: 启用调试模式，输出详细日志

#### documentation

- **类型**: `Object | Boolean`
- **可选**: 是
- **默认**: `false`
- **说明**: 生成设计令牌文档

```javascript
documentation: {
  path: './tokens-docs.html', // 文档输出路径
  title: 'Design Tokens Documentation', // 文档标题
  open: false // 是否在开发服务器启动时打开文档
}
```

## 完整配置示例

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import designTokens from 'vite-plugin-design-tokens'

export default defineConfig({
  plugins: [
    designTokens({
      // 数据源配置
      figma: {
        fileId: 'abc123def456',
        token: process.env.FIGMA_ACCESS_TOKEN,
        page: 'Design Tokens',
        pollingInterval: 30000
      },
      
      // 备用本地文件
      json: {
        path: './fallback-tokens.json',
        watch: true
      },
      
      // 输出配置
      output: {
        css: {
          path: './src/styles/tokens.css',
          selector: ':root',
          prefix: 'td'
        },
        ts: {
          path: './src/utils/tokens.ts',
          typeDeclaration: true
        },
        tailwind: {
          path: './tailwind.tokens.js',
          extend: true
        }
      },
      
      // 转换器
      transformers: [
        {
          name: 'color-rgb',
          transform: (token) => {
            if (token.type === 'color') {
              return {
                ...token,
                value: hexToRgb(token.value)
              }
            }
            return token
          }
        }
      ],
      
      // 文档生成
      documentation: {
        path: './design-tokens-docs.html',
        title: '项目设计令牌',
        open: process.env.NODE_ENV === 'development'
      },
      
      // 调试模式
      debug: process.env.NODE_ENV === 'development'
    })
  ]
})
```

## 设计令牌格式

插件支持符合 W3C 设计令牌格式组标准的 JSON 文件：

```json
{
  "color": {
    "primary": {
      "base": {
        "$value": "#0066ff",
        "$type": "color",
        "$description": "主品牌颜色"
      },
      "light": {
        "$value": "#3399ff",
        "$type": "color"
      }
    }
  },
  "spacing": {
    "small": {
      "$value": "8px",
      "$type": "dimension"
    },
    "medium": {
      "$value": "16px",
      "$type": "dimension"
    }
  }
}
```

## 输出示例

### CSS 输出示例

```css
:root {
  --td-color-primary-base: #0066ff;
  --td-color-primary-light: #3399ff;
  --td-spacing-small: 8px;
  --td-spacing-medium: 16px;
}

[data-theme="dark"] {
  --td-color-primary-base: #0044cc;
  --td-color-primary-light: #2266dd;
}
```

### TypeScript 输出示例

```typescript
// tokens.ts
export const Tokens = {
  color: {
    primary: {
      base: '#0066ff',
      light: '#3399ff'
    }
  },
  spacing: {
    small: '8px',
    medium: '16px'
  }
} as const;

// tokens.d.ts
export interface DesignTokens {
  readonly color: {
    readonly primary: {
      readonly base: '#0066ff';
      readonly light: '#3399ff';
    };
  };
  readonly spacing: {
    readonly small: '8px';
    readonly medium: '16px';
  };
}
export declare const Tokens: DesignTokens;
```

### Tailwind 配置输出示例

```javascript
// tailwind.tokens.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          base: '#0066ff',
          light: '#3399ff'
        }
      },
      spacing: {
        small: '8px',
        medium: '16px'
      }
    }
  }
}
```

## API 参考

### Figma 转换器

插件内置了 Figma 到设计令牌的转换逻辑：

- **颜色样式** → 颜色令牌
- **文本样式** → 排版令牌
- **效果样式** → 阴影/模糊令牌
- **网格样式** → 布局令牌

### 上下文对象

转换器接收的上下文对象包含以下信息：

```typescript
interface TransformContext {
  mode: 'build' | 'serve'; // 运行模式
  root: string; // 项目根目录
  outputFormats: string[]; // 启用的输出格式
  tokenSet: TokenSet; // 完整的令牌集合
}
```

## 开发指南

### 自定义转换器

您可以创建自定义转换器来处理特定类型的令牌：

```javascript
// custom-transformer.js
export function customColorTransformer(token, context) {
  if (token.$type === 'color') {
    return {
      ...token,
      $value: convertToRgb(token.$value),
      attributes: {
        ...token.attributes,
        rgb: hexToRgb(token.$value)
      }
    }
  }
  return token
}

// 在配置中使用
import { customColorTransformer } from './custom-transformer'

export default defineConfig({
  plugins: [
    designTokens({
      transformers: [customColorTransformer]
    })
  ]
})
```

### 插件开发模式

```bash
# 克隆仓库
git clone https://github.com/your-org/vite-plugin-design-tokens.git

# 安装依赖
npm install

# 运行示例
npm run dev

# 构建插件
npm run build

# 运行测试
npm test
```

## 故障排除

### 常见问题

1. **Figma API 权限错误**
   - 确保使用正确的 Personal Access Token
   - 确认 Token 有访问目标文件的权限

2. **文件监听不工作**
   - 检查文件路径是否正确
   - 确认有足够的文件系统权限

3. **类型生成错误**
   - 检查令牌格式是否符合标准
   - 确认没有循环引用

### 调试模式

启用调试模式获取详细日志：

```javascript
designTokens({
  debug: true
})
```


## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 更新日志

### v1.0.0 (2023-11-01)
- 初始版本发布
- 支持 Figma API 和本地 JSON 文件
- 支持 CSS、TypeScript、Tailwind 输出格式
- 类型声明生成
- 热重载支持

## 支持

如有问题或建议，请通过以下方式联系：

- [创建 Issue](https://github.com/your-org/vite-plugin-design-tokens/issues)
- [Discussions](https://github.com/your-org/vite-plugin-design-tokens/discussions)
- 邮箱: <EMAIL>

## 相关项目

- [Figma API Client](https://github.com/figma/api-client)
- [Style Dictionary](https://github.com/amzn/style-dictionary)
- [Design Tokens W3C Community Group](https://www.w3.org/community/design-tokens/)

---

*本文档最后更新于: 2023-11-01*