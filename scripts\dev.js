#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

// 构建插件
console.log('Building plugin...')
const buildProcess = spawn('npm', ['run', 'build'], {
  stdio: 'inherit',
  shell: true
})

buildProcess.on('close', (code) => {
  if (code !== 0) {
    console.error('Build failed')
    process.exit(1)
  }
  
  console.log('Plugin built successfully')
  console.log('Starting example...')
  
  // 启动示例项目
  const exampleProcess = spawn('npm', ['run', 'dev'], {
    cwd: path.join(__dirname, '../example'),
    stdio: 'inherit',
    shell: true
  })
  
  exampleProcess.on('close', (code) => {
    process.exit(code)
  })
})
