import type { ResolvedConfig, ViteDevServer } from 'vite'
import type { IncomingMessage, ServerResponse } from 'http'
import { DesignTokensPluginOptions, TokenSet, TransformContext } from './types'
import { FigmaClient } from './figma/client'
import { TokenTransformer } from './transformers/token-transformer'
import { OutputGenerator } from './generators/output-generator'
import { FileWatcher } from './utils/file-watcher'
import { Logger } from './utils/logger'
import path from 'path'
import fs from 'fs/promises'

export class DesignTokensCore {
  private figmaClient?: FigmaClient
  private tokenTransformer: TokenTransformer
  private outputGenerator: OutputGenerator
  private fileWatcher?: FileWatcher
  private currentTokens: TokenSet = {}

  constructor(
    private options: DesignTokensPluginOptions,
    private config: ResolvedConfig,
    private logger: Logger
  ) {
    this.tokenTransformer = new TokenTransformer(options.transformers || [], logger)
    this.outputGenerator = new OutputGenerator(options.output || {}, config.root, logger)
    
    if (options.figma) {
      this.figmaClient = new FigmaClient(options.figma, logger)
    }
  }

  async initialize() {
    this.logger.debug('Initializing design tokens core')
    
    // 初始化输出生成器
    await this.outputGenerator.initialize()
  }

  async generateTokens() {
    this.logger.debug('Generating design tokens')
    
    let tokens: TokenSet = {}

    // 从 Figma 获取令牌
    if (this.figmaClient) {
      try {
        const figmaTokens = await this.figmaClient.fetchTokens()
        tokens = { ...tokens, ...figmaTokens }
        this.logger.info(`Fetched ${Object.keys(figmaTokens).length} token groups from Figma`)
      } catch (error) {
        this.logger.error('Failed to fetch tokens from Figma:', error)
      }
    }

    // 从 JSON 文件获取令牌
    if (this.options.json) {
      try {
        const jsonPath = typeof this.options.json === 'string' 
          ? this.options.json 
          : this.options.json.path
        const fullPath = path.resolve(this.config.root, jsonPath)
        const jsonContent = await fs.readFile(fullPath, 'utf-8')
        const jsonTokens = JSON.parse(jsonContent)
        tokens = { ...tokens, ...jsonTokens }
        this.logger.info(`Loaded tokens from ${jsonPath}`)
      } catch (error) {
        this.logger.error('Failed to load tokens from JSON:', error)
      }
    }

    // 转换令牌
    const context: TransformContext = {
      mode: this.config.command === 'build' ? 'build' : 'serve',
      root: this.config.root,
      outputFormats: this.getEnabledOutputFormats(),
      tokenSet: tokens
    }

    this.currentTokens = this.tokenTransformer.transform(tokens, context)

    // 生成输出文件
    await this.outputGenerator.generate(this.currentTokens)
  }

  async startWatching(server: ViteDevServer) {
    this.logger.debug('Starting file watching')
    
    const watchPaths: string[] = []

    // 监听 JSON 文件
    if (this.options.json) {
      const jsonPath = typeof this.options.json === 'string' 
        ? this.options.json 
        : this.options.json.path
      watchPaths.push(path.resolve(this.config.root, jsonPath))
    }

    if (watchPaths.length > 0) {
      this.fileWatcher = new FileWatcher(watchPaths, this.logger)
      this.fileWatcher.on('change', async () => {
        await this.generateTokens()
        server.ws.send({
          type: 'full-reload'
        })
      })
    }

    // 启动 Figma 轮询
    if (this.figmaClient && this.options.figma?.pollingInterval) {
      setInterval(async () => {
        try {
          const newTokens = await this.figmaClient!.fetchTokens()
          if (JSON.stringify(newTokens) !== JSON.stringify(this.currentTokens)) {
            this.logger.info('Figma tokens updated')
            await this.generateTokens()
            server.ws.send({
              type: 'full-reload'
            })
          }
        } catch (error) {
          this.logger.error('Failed to poll Figma for updates:', error)
        }
      }, this.options.figma.pollingInterval)
    }
  }

  async handleFileChange(filePath: string): Promise<boolean> {
    // 检查是否是我们关心的文件
    if (this.options.json) {
      const jsonPath = typeof this.options.json === 'string' 
        ? this.options.json 
        : this.options.json.path
      const fullPath = path.resolve(this.config.root, jsonPath)
      
      if (filePath === fullPath) {
        await this.generateTokens()
        return true
      }
    }
    
    return false
  }

  handleDocumentationRequest(req: IncomingMessage, res: ServerResponse, next: () => void) {
    // 处理文档页面请求
    if (req.url === '/__design-tokens') {
      res.setHeader('Content-Type', 'text/html')
      res.end(this.generateDocumentationHTML())
    } else {
      next()
    }
  }

  async cleanup() {
    this.logger.debug('Cleaning up design tokens core')
    
    if (this.fileWatcher) {
      this.fileWatcher.close()
    }
  }

  private getEnabledOutputFormats(): string[] {
    const formats: string[] = []
    const output = this.options.output || {}
    
    if (output.css) formats.push('css')
    if (output.ts) formats.push('ts')
    if (output.tailwind) formats.push('tailwind')
    if (output.json) formats.push('json')
    
    return formats
  }

  private generateDocumentationHTML(): string {
    const title = typeof this.options.documentation === 'object' 
      ? this.options.documentation.title || 'Design Tokens'
      : 'Design Tokens'

    return `
<!DOCTYPE html>
<html>
<head>
  <title>${title}</title>
  <style>
    body { font-family: system-ui, sans-serif; margin: 2rem; }
    .token { margin: 1rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 4px; }
    .token-name { font-weight: bold; margin-bottom: 0.5rem; }
    .token-value { font-family: monospace; background: #f5f5f5; padding: 0.25rem; }
  </style>
</head>
<body>
  <h1>${title}</h1>
  <div id="tokens">
    ${this.renderTokensHTML(this.currentTokens)}
  </div>
</body>
</html>`
  }

  private renderTokensHTML(tokens: TokenSet, prefix = ''): string {
    return Object.entries(tokens)
      .map(([key, value]) => {
        const fullKey = prefix ? `${prefix}.${key}` : key
        
        if (value && typeof value === 'object' && '$value' in value) {
          return `
            <div class="token">
              <div class="token-name">${fullKey}</div>
              <div class="token-value">${JSON.stringify(value.$value)}</div>
              ${value.$description ? `<div class="token-description">${value.$description}</div>` : ''}
            </div>`
        } else if (typeof value === 'object') {
          return `
            <h3>${fullKey}</h3>
            ${this.renderTokensHTML(value as TokenSet, fullKey)}`
        }
        
        return ''
      })
      .join('')
  }
}
