import { FigmaConfig, FigmaFile, TokenSet } from '../types'
import { Logger } from '../utils/logger'
import { FigmaTokenExtractor } from './token-extractor'

export class FigmaClient {
  private baseUrl = 'https://api.figma.com/v1'
  private tokenExtractor: FigmaTokenExtractor

  constructor(
    private config: FigmaConfig,
    private logger: Logger
  ) {
    this.tokenExtractor = new FigmaTokenExtractor(logger)
  }

  async fetchTokens(): Promise<TokenSet> {
    this.logger.debug(`Fetching tokens from Figma file: ${this.config.fileId}`)

    try {
      // 获取文件信息
      const file = await this.fetchFile()
      
      // 获取样式信息
      const styles = await this.fetchStyles()
      
      // 提取设计令牌
      const tokens = this.tokenExtractor.extractTokens(file, styles)
      
      this.logger.debug(`Extracted ${Object.keys(tokens).length} token groups from Figma`)
      return tokens
    } catch (error) {
      this.logger.error('Failed to fetch tokens from Figma:', error)
      throw error
    }
  }

  private async fetchFile(): Promise<FigmaFile> {
    const url = `${this.baseUrl}/files/${this.config.fileId}`
    const response = await this.makeRequest(url)
    
    if (!response.ok) {
      throw new Error(`Failed to fetch Figma file: ${response.status} ${response.statusText}`)
    }
    
    return response.json()
  }

  private async fetchStyles(): Promise<Record<string, any>> {
    const url = `${this.baseUrl}/files/${this.config.fileId}/styles`
    const response = await this.makeRequest(url)

    if (!response.ok) {
      throw new Error(`Failed to fetch Figma styles: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    // 获取样式的详细信息
    const styles = data.meta?.styles || {}
    const styleDetails: Record<string, any> = {}

    // 批量获取样式详情
    for (const [styleId, style] of Object.entries(styles)) {
      try {
        const styleDetail = await this.fetchStyleDetail(styleId)
        styleDetails[styleId] = { ...style, ...styleDetail }
      } catch (error) {
        this.logger.warn(`Failed to fetch style detail for ${styleId}:`, error)
        styleDetails[styleId] = style
      }
    }

    return styleDetails
  }

  private async fetchStyleDetail(styleId: string): Promise<any> {
    const url = `${this.baseUrl}/styles/${styleId}`
    const response = await this.makeRequest(url)

    if (!response.ok) {
      throw new Error(`Failed to fetch style detail: ${response.status}`)
    }

    return response.json()
  }

  private async makeRequest(url: string): Promise<Response> {
    const headers: Record<string, string> = {
      'X-Figma-Token': this.config.token,
      'Content-Type': 'application/json',
      ...this.config.headers
    }

    this.logger.debug(`Making request to: ${url}`)
    
    const response = await fetch(url, {
      method: 'GET',
      headers
    })

    return response
  }

  async testConnection(): Promise<boolean> {
    try {
      const url = `${this.baseUrl}/me`
      const response = await this.makeRequest(url)
      return response.ok
    } catch (error) {
      this.logger.error('Failed to test Figma connection:', error)
      return false
    }
  }
}
