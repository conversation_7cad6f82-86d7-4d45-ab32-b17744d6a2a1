import { 
  FigmaFile, 
  FigmaStyle, 
  FigmaColor, 
  FigmaPaint, 
  FigmaEffect,
  FigmaTypeStyle,
  TokenSet, 
  DesignToken 
} from '../types'
import { Logger } from '../utils/logger'

export class FigmaTokenExtractor {
  constructor(private logger: Logger) {}

  extractTokens(file: FigmaFile, styles: Record<string, FigmaStyle>): TokenSet {
    this.logger.debug('Extracting tokens from Figma file')
    
    const tokens: TokenSet = {}

    // 提取颜色令牌
    const colorTokens = this.extractColorTokens(styles)
    if (Object.keys(colorTokens).length > 0) {
      tokens.color = colorTokens
    }

    // 提取文本样式令牌
    const textTokens = this.extractTextTokens(styles)
    if (Object.keys(textTokens).length > 0) {
      tokens.typography = textTokens
    }

    // 提取效果令牌（阴影等）
    const effectTokens = this.extractEffectTokens(styles)
    if (Object.keys(effectTokens).length > 0) {
      tokens.shadow = effectTokens
    }

    return tokens
  }

  private extractColorTokens(styles: Record<string, FigmaStyle>): TokenSet {
    const colorTokens: TokenSet = {}

    Object.entries(styles).forEach(([key, style]) => {
      if (style.styleType === 'FILL') {
        const tokenPath = this.parseStyleName(style.name)
        const colorValue = this.extractColorFromStyle(style)
        
        if (colorValue) {
          this.setNestedToken(colorTokens, tokenPath, {
            $value: colorValue,
            $type: 'color',
            $description: style.description || undefined
          })
        }
      }
    })

    return colorTokens
  }

  private extractTextTokens(styles: Record<string, FigmaStyle>): TokenSet {
    const textTokens: TokenSet = {}

    Object.entries(styles).forEach(([key, style]) => {
      if (style.styleType === 'TEXT') {
        const tokenPath = this.parseStyleName(style.name)
        const textStyle = this.extractTextStyleFromStyle(style)
        
        if (textStyle) {
          this.setNestedToken(textTokens, tokenPath, {
            $value: textStyle,
            $type: 'typography',
            $description: style.description || undefined
          })
        }
      }
    })

    return textTokens
  }

  private extractEffectTokens(styles: Record<string, FigmaStyle>): TokenSet {
    const effectTokens: TokenSet = {}

    Object.entries(styles).forEach(([key, style]) => {
      if (style.styleType === 'EFFECT') {
        const tokenPath = this.parseStyleName(style.name)
        const effectValue = this.extractEffectFromStyle(style)
        
        if (effectValue) {
          this.setNestedToken(effectTokens, tokenPath, {
            $value: effectValue,
            $type: 'shadow',
            $description: style.description || undefined
          })
        }
      }
    })

    return effectTokens
  }

  private parseStyleName(name: string): string[] {
    // 将 Figma 样式名称转换为令牌路径
    // 例如: "Color/Primary/Base" -> ["primary", "base"]
    return name
      .toLowerCase()
      .split('/')
      .filter(part => part.trim() !== '' && part.toLowerCase() !== 'color' && part.toLowerCase() !== 'text')
      .map(part => part.trim().replace(/\s+/g, '-'))
  }

  private extractColorFromStyle(style: FigmaStyle): string | null {
    // 这里需要从实际的 Figma 样式数据中提取颜色
    // 由于我们只有样式元数据，这里返回一个占位符
    // 在实际实现中，需要通过额外的 API 调用获取样式的具体数据
    return '#000000' // 占位符
  }

  private extractTextStyleFromStyle(style: FigmaStyle): object | null {
    // 提取文本样式信息
    return {
      fontFamily: 'Inter',
      fontSize: '16px',
      fontWeight: 400,
      lineHeight: '24px'
    }
  }

  private extractEffectFromStyle(style: FigmaStyle): string | null {
    // 提取效果样式信息
    return '0 2px 4px rgba(0, 0, 0, 0.1)'
  }

  private setNestedToken(tokens: TokenSet, path: string[], token: DesignToken) {
    let current = tokens
    
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!current[key] || typeof current[key] !== 'object' || '$value' in current[key]) {
        current[key] = {}
      }
      current = current[key] as TokenSet
    }
    
    const finalKey = path[path.length - 1]
    current[finalKey] = token
  }

  private figmaColorToHex(color: FigmaColor): string {
    const r = Math.round(color.r * 255)
    const g = Math.round(color.g * 255)
    const b = Math.round(color.b * 255)
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  }

  private figmaColorToRgba(color: FigmaColor): string {
    const r = Math.round(color.r * 255)
    const g = Math.round(color.g * 255)
    const b = Math.round(color.b * 255)
    
    return color.a === 1 
      ? `rgb(${r}, ${g}, ${b})`
      : `rgba(${r}, ${g}, ${b}, ${color.a})`
  }
}
