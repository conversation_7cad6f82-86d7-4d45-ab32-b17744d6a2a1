import { CssOutputConfig, TokenSet, DesignToken } from '../types'
import { Logger } from '../utils/logger'
import path from 'path'
import fs from 'fs/promises'

export class CssGenerator {
  private config: CssOutputConfig

  constructor(
    config: CssOutputConfig | boolean,
    private rootDir: string,
    private logger: Logger
  ) {
    this.config = typeof config === 'boolean' 
      ? { path: './src/styles/tokens.css' }
      : config
  }

  async generate(tokens: TokenSet) {
    this.logger.debug('Generating CSS output')
    
    const css = this.generateCss(tokens)
    const outputPath = path.resolve(this.rootDir, this.config.path)
    
    await this.ensureDirectoryExists(outputPath)
    await fs.writeFile(outputPath, css, 'utf-8')
    
    this.logger.info(`CSS tokens generated: ${outputPath}`)
  }

  private generateCss(tokens: TokenSet): string {
    const selector = this.config.selector || ':root'
    const prefix = this.config.prefix ? `${this.config.prefix}-` : ''
    
    let css = `${selector} {\n`
    css += this.generateCssVariables(tokens, prefix)
    css += '}\n'
    
    // 生成暗色主题（如果配置了）
    if (this.config.darkThemeSelector) {
      const darkTokens = this.extractDarkThemeTokens(tokens)
      if (Object.keys(darkTokens).length > 0) {
        css += `\n${this.config.darkThemeSelector} {\n`
        css += this.generateCssVariables(darkTokens, prefix)
        css += '}\n'
      }
    }
    
    return css
  }

  private generateCssVariables(tokens: TokenSet, prefix: string, path: string[] = []): string {
    let css = ''
    
    for (const [key, value] of Object.entries(tokens)) {
      const currentPath = [...path, key]
      
      if (this.isDesignToken(value)) {
        const variableName = this.createVariableName(currentPath, prefix)
        const variableValue = this.formatCssValue(value)
        css += `  --${variableName}: ${variableValue};\n`
      } else if (typeof value === 'object' && value !== null) {
        css += this.generateCssVariables(value as TokenSet, prefix, currentPath)
      }
    }
    
    return css
  }

  private createVariableName(path: string[], prefix: string): string {
    const name = path.join('-').toLowerCase().replace(/[^a-z0-9-]/g, '-')
    return `${prefix}${name}`
  }

  private formatCssValue(token: DesignToken): string {
    const value = token.$value
    
    if (typeof value === 'string') {
      return value
    }
    
    if (typeof value === 'number') {
      // 对于数值，根据类型添加适当的单位
      if (token.$type === 'dimension') {
        return `${value}px`
      }
      return value.toString()
    }
    
    if (typeof value === 'object') {
      // 处理复杂对象（如渐变、阴影等）
      if (token.$type === 'shadow') {
        return this.formatShadowValue(value)
      }
      
      if (token.$type === 'typography') {
        return this.formatTypographyValue(value)
      }
    }
    
    return JSON.stringify(value)
  }

  private formatShadowValue(shadow: any): string {
    if (typeof shadow === 'string') {
      return shadow
    }
    
    // 处理阴影对象格式
    const { offsetX = 0, offsetY = 0, blur = 0, spread = 0, color = '#000000' } = shadow
    return `${offsetX}px ${offsetY}px ${blur}px ${spread}px ${color}`
  }

  private formatTypographyValue(typography: any): string {
    if (typeof typography === 'string') {
      return typography
    }
    
    // 对于排版，我们可能需要分别处理各个属性
    // 这里返回字体简写形式
    const { fontWeight = 400, fontSize = '16px', lineHeight = 'normal', fontFamily = 'inherit' } = typography
    return `${fontWeight} ${fontSize}/${lineHeight} ${fontFamily}`
  }

  private extractDarkThemeTokens(tokens: TokenSet): TokenSet {
    // 提取暗色主题令牌
    // 这里可以实现更复杂的逻辑来识别暗色主题变体
    const darkTokens: TokenSet = {}
    
    this.walkTokens(tokens, (token, path) => {
      // 查找包含 'dark' 关键字的令牌
      if (path.some(p => p.toLowerCase().includes('dark'))) {
        this.setNestedToken(darkTokens, path, token)
      }
    })
    
    return darkTokens
  }

  private walkTokens(tokens: TokenSet, callback: (token: DesignToken, path: string[]) => void, path: string[] = []) {
    for (const [key, value] of Object.entries(tokens)) {
      const currentPath = [...path, key]
      
      if (this.isDesignToken(value)) {
        callback(value, currentPath)
      } else if (typeof value === 'object' && value !== null) {
        this.walkTokens(value as TokenSet, callback, currentPath)
      }
    }
  }

  private setNestedToken(tokens: TokenSet, path: string[], token: DesignToken) {
    let current = tokens
    
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!current[key] || typeof current[key] !== 'object' || '$value' in current[key]) {
        current[key] = {}
      }
      current = current[key] as TokenSet
    }
    
    const finalKey = path[path.length - 1]
    current[finalKey] = token
  }

  private isDesignToken(value: any): value is DesignToken {
    return typeof value === 'object' && value !== null && '$value' in value
  }

  private async ensureDirectoryExists(filePath: string) {
    const dir = path.dirname(filePath)
    try {
      await fs.access(dir)
    } catch {
      await fs.mkdir(dir, { recursive: true })
    }
  }
}
