import { DocumentationConfig, TokenSet, DesignToken } from '../types'
import { Logger } from '../utils/logger'
import path from 'path'
import fs from 'fs/promises'

export class DocumentationGenerator {
  private config: DocumentationConfig

  constructor(
    config: DocumentationConfig | boolean,
    private rootDir: string,
    private logger: Logger
  ) {
    this.config = typeof config === 'boolean' 
      ? { path: './design-tokens-docs.html', title: 'Design Tokens Documentation', open: false }
      : { title: 'Design Tokens Documentation', open: false, ...config }
  }

  async generate(tokens: TokenSet) {
    this.logger.debug('Generating documentation')
    
    const html = this.generateHTML(tokens)
    const outputPath = path.resolve(this.rootDir, this.config.path)
    
    await this.ensureDirectoryExists(outputPath)
    await fs.writeFile(outputPath, html, 'utf-8')
    
    this.logger.info(`Documentation generated: ${outputPath}`)
  }

  private generateHTML(tokens: TokenSet): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${this.config.title}</title>
  <style>
    ${this.generateCSS()}
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>${this.config.title}</h1>
      <p>Auto-generated design tokens documentation</p>
    </header>
    
    <main>
      ${this.generateTokensHTML(tokens)}
    </main>
    
    <footer>
      <p>Generated on ${new Date().toLocaleString()}</p>
    </footer>
  </div>
  
  <script>
    ${this.generateJavaScript()}
  </script>
</body>
</html>`
  }

  private generateCSS(): string {
    return `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #333;
      background: #f8f9fa;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    header {
      text-align: center;
      margin-bottom: 3rem;
      padding-bottom: 2rem;
      border-bottom: 2px solid #e9ecef;
    }
    
    h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
      color: #2c3e50;
    }
    
    h2 {
      font-size: 1.8rem;
      margin: 2rem 0 1rem;
      color: #34495e;
      border-bottom: 1px solid #bdc3c7;
      padding-bottom: 0.5rem;
    }
    
    h3 {
      font-size: 1.3rem;
      margin: 1.5rem 0 0.5rem;
      color: #7f8c8d;
    }
    
    .token-category {
      margin-bottom: 3rem;
    }
    
    .token-group {
      margin-bottom: 2rem;
    }
    
    .token {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .token-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }
    
    .token-name {
      font-weight: 600;
      color: #2c3e50;
    }
    
    .token-type {
      background: #3498db;
      color: white;
      padding: 0.2rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
    }
    
    .token-value {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      background: #f8f9fa;
      padding: 0.5rem;
      border-radius: 4px;
      border: 1px solid #e9ecef;
      margin: 0.5rem 0;
    }
    
    .token-preview {
      margin: 0.5rem 0;
      padding: 0.5rem;
      border-radius: 4px;
      border: 1px solid #e9ecef;
    }
    
    .color-preview {
      width: 100%;
      height: 40px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    
    .typography-preview {
      border: 1px solid #e9ecef;
      padding: 1rem;
      border-radius: 4px;
    }
    
    .token-description {
      color: #7f8c8d;
      font-style: italic;
      margin-top: 0.5rem;
    }
    
    footer {
      text-align: center;
      margin-top: 3rem;
      padding-top: 2rem;
      border-top: 1px solid #e9ecef;
      color: #7f8c8d;
    }
    `
  }

  private generateTokensHTML(tokens: TokenSet): string {
    let html = ''
    
    for (const [categoryKey, categoryValue] of Object.entries(tokens)) {
      if (typeof categoryValue === 'object' && categoryValue !== null) {
        html += `<div class="token-category">
          <h2>${this.formatCategoryName(categoryKey)}</h2>
          ${this.generateCategoryHTML(categoryValue as TokenSet, categoryKey)}
        </div>`
      }
    }
    
    return html
  }

  private generateCategoryHTML(tokens: TokenSet, categoryKey: string): string {
    let html = ''
    
    for (const [key, value] of Object.entries(tokens)) {
      if (this.isDesignToken(value)) {
        html += this.generateTokenHTML(key, value, categoryKey)
      } else if (typeof value === 'object' && value !== null) {
        html += `<div class="token-group">
          <h3>${this.formatTokenName(key)}</h3>
          ${this.generateCategoryHTML(value as TokenSet, categoryKey)}
        </div>`
      }
    }
    
    return html
  }

  private generateTokenHTML(name: string, token: DesignToken, category: string): string {
    const preview = this.generateTokenPreview(token, category)
    
    return `<div class="token">
      <div class="token-header">
        <span class="token-name">${this.formatTokenName(name)}</span>
        <span class="token-type">${token.$type || 'unknown'}</span>
      </div>
      <div class="token-value">${this.formatTokenValue(token.$value)}</div>
      ${preview}
      ${token.$description ? `<div class="token-description">${token.$description}</div>` : ''}
    </div>`
  }

  private generateTokenPreview(token: DesignToken, category: string): string {
    const type = token.$type || category
    
    switch (type) {
      case 'color':
        return `<div class="token-preview">
          <div class="color-preview" style="background-color: ${token.$value};"></div>
        </div>`
      
      case 'typography':
        return this.generateTypographyPreview(token)
      
      case 'shadow':
        return `<div class="token-preview">
          <div style="width: 100px; height: 40px; background: #fff; box-shadow: ${token.$value};"></div>
        </div>`
      
      default:
        return ''
    }
  }

  private generateTypographyPreview(token: DesignToken): string {
    const value = token.$value
    
    if (typeof value === 'object') {
      const { fontFamily, fontSize, fontWeight, lineHeight } = value as any
      const style = `
        font-family: ${fontFamily || 'inherit'};
        font-size: ${fontSize || '16px'};
        font-weight: ${fontWeight || 'normal'};
        line-height: ${lineHeight || 'normal'};
      `.trim()
      
      return `<div class="token-preview">
        <div class="typography-preview" style="${style}">
          The quick brown fox jumps over the lazy dog
        </div>
      </div>`
    }
    
    return ''
  }

  private formatCategoryName(name: string): string {
    return name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1')
  }

  private formatTokenName(name: string): string {
    return name.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  private formatTokenValue(value: any): string {
    if (typeof value === 'string') {
      return value
    }
    
    return JSON.stringify(value, null, 2)
  }

  private generateJavaScript(): string {
    return `
    // Copy token value to clipboard
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('token-value')) {
        const text = e.target.textContent;
        navigator.clipboard.writeText(text).then(() => {
          const original = e.target.style.background;
          e.target.style.background = '#d4edda';
          setTimeout(() => {
            e.target.style.background = original;
          }, 200);
        });
      }
    });
    `
  }

  private isDesignToken(value: any): value is DesignToken {
    return typeof value === 'object' && value !== null && '$value' in value
  }

  private async ensureDirectoryExists(filePath: string) {
    const dir = path.dirname(filePath)
    try {
      await fs.access(dir)
    } catch {
      await fs.mkdir(dir, { recursive: true })
    }
  }
}
