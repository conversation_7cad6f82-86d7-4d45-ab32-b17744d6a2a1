import { JsonOutputConfig, TokenSet } from '../types'
import { Logger } from '../utils/logger'
import path from 'path'
import fs from 'fs/promises'

export class JsonGenerator {
  private config: JsonOutputConfig

  constructor(
    config: JsonOutputConfig | boolean,
    private rootDir: string,
    private logger: Logger
  ) {
    this.config = typeof config === 'boolean' 
      ? { path: './src/tokens/output.json', minify: false }
      : { minify: false, ...config }
  }

  async generate(tokens: TokenSet) {
    this.logger.debug('Generating JSON output')
    
    const jsonContent = this.config.minify 
      ? JSON.stringify(tokens)
      : JSON.stringify(tokens, null, 2)
    
    const outputPath = path.resolve(this.rootDir, this.config.path)
    
    await this.ensureDirectoryExists(outputPath)
    await fs.writeFile(outputPath, jsonContent, 'utf-8')
    
    this.logger.info(`JSON tokens generated: ${outputPath}`)
  }

  private async ensureDirectoryExists(filePath: string) {
    const dir = path.dirname(filePath)
    try {
      await fs.access(dir)
    } catch {
      await fs.mkdir(dir, { recursive: true })
    }
  }
}
