import { OutputConfig, TokenSet } from '../types'
import { Logger } from '../utils/logger'
import { CssGenerator } from './css-generator'
import { TypeScriptGenerator } from './typescript-generator'
import { TailwindGenerator } from './tailwind-generator'
import { JsonGenerator } from './json-generator'
import path from 'path'
import fs from 'fs/promises'

export class OutputGenerator {
  private cssGenerator?: CssGenerator
  private tsGenerator?: TypeScriptGenerator
  private tailwindGenerator?: TailwindGenerator
  private jsonGenerator?: JsonGenerator

  constructor(
    private config: OutputConfig,
    private rootDir: string,
    private logger: Logger
  ) {}

  async initialize() {
    this.logger.debug('Initializing output generators')
    
    if (this.config.css) {
      this.cssGenerator = new CssGenerator(this.config.css, this.rootDir, this.logger)
    }
    
    if (this.config.ts) {
      this.tsGenerator = new TypeScriptGenerator(this.config.ts, this.rootDir, this.logger)
    }
    
    if (this.config.tailwind) {
      this.tailwindGenerator = new TailwindGenerator(this.config.tailwind, this.rootDir, this.logger)
    }
    
    if (this.config.json) {
      this.jsonGenerator = new JsonGenerator(this.config.json, this.rootDir, this.logger)
    }
  }

  async generate(tokens: TokenSet) {
    this.logger.debug('Generating output files')
    
    const promises: Promise<void>[] = []

    if (this.cssGenerator) {
      promises.push(this.cssGenerator.generate(tokens))
    }
    
    if (this.tsGenerator) {
      promises.push(this.tsGenerator.generate(tokens))
    }
    
    if (this.tailwindGenerator) {
      promises.push(this.tailwindGenerator.generate(tokens))
    }
    
    if (this.jsonGenerator) {
      promises.push(this.jsonGenerator.generate(tokens))
    }

    await Promise.all(promises)
    this.logger.info('All output files generated successfully')
  }

  async ensureDirectoryExists(filePath: string) {
    const dir = path.dirname(filePath)
    try {
      await fs.access(dir)
    } catch {
      await fs.mkdir(dir, { recursive: true })
      this.logger.debug(`Created directory: ${dir}`)
    }
  }
}
