import { TailwindOutputConfig, TokenSet, DesignToken } from '../types'
import { Logger } from '../utils/logger'
import path from 'path'
import fs from 'fs/promises'

export class TailwindGenerator {
  private config: TailwindOutputConfig

  constructor(
    config: TailwindOutputConfig | boolean,
    private rootDir: string,
    private logger: Logger
  ) {
    this.config = typeof config === 'boolean' 
      ? { path: './tailwind.tokens.js', extend: true }
      : { extend: true, ...config }
  }

  async generate(tokens: TokenSet) {
    this.logger.debug('Generating Tailwind CSS output')
    
    const tailwindConfig = this.generateTailwindConfig(tokens)
    const outputPath = path.resolve(this.rootDir, this.config.path)
    
    await this.ensureDirectoryExists(outputPath)
    await fs.writeFile(outputPath, tailwindConfig, 'utf-8')
    
    this.logger.info(`Tailwind config generated: ${outputPath}`)
  }

  private generateTailwindConfig(tokens: TokenSet): string {
    const config = this.convertToTailwindConfig(tokens)
    
    const configObject = this.config.extend 
      ? { theme: { extend: config } }
      : { theme: config }

    return `// This file is auto-generated. Do not edit manually.
module.exports = ${JSON.stringify(configObject, null, 2)};
`
  }

  private convertToTailwindConfig(tokens: TokenSet): any {
    const config: any = {}
    
    for (const [category, categoryTokens] of Object.entries(tokens)) {
      if (typeof categoryTokens === 'object' && categoryTokens !== null && !this.isDesignToken(categoryTokens)) {
        const tailwindCategory = this.mapCategoryToTailwind(category)
        if (tailwindCategory) {
          config[tailwindCategory] = this.convertTokensToTailwindValues(categoryTokens as TokenSet)
        }
      }
    }
    
    return config
  }

  private mapCategoryToTailwind(category: string): string | null {
    const categoryMap: Record<string, string> = {
      'color': 'colors',
      'colors': 'colors',
      'spacing': 'spacing',
      'size': 'spacing',
      'fontSize': 'fontSize',
      'fontFamily': 'fontFamily',
      'fontWeight': 'fontWeight',
      'lineHeight': 'lineHeight',
      'letterSpacing': 'letterSpacing',
      'borderRadius': 'borderRadius',
      'borderWidth': 'borderWidth',
      'boxShadow': 'boxShadow',
      'shadow': 'boxShadow',
      'opacity': 'opacity',
      'zIndex': 'zIndex'
    }
    
    return categoryMap[category.toLowerCase()] || null
  }

  private convertTokensToTailwindValues(tokens: TokenSet): any {
    const result: any = {}
    
    for (const [key, value] of Object.entries(tokens)) {
      if (this.isDesignToken(value)) {
        result[key] = this.formatTailwindValue(value)
      } else if (typeof value === 'object' && value !== null) {
        result[key] = this.convertTokensToTailwindValues(value as TokenSet)
      }
    }
    
    return result
  }

  private formatTailwindValue(token: DesignToken): any {
    const value = token.$value
    
    // 对于颜色，直接返回值
    if (token.$type === 'color') {
      return value
    }
    
    // 对于尺寸，确保有单位
    if (token.$type === 'dimension') {
      if (typeof value === 'number') {
        return `${value}px`
      }
      return value
    }
    
    // 对于字体权重，转换为数值
    if (token.$type === 'fontWeight') {
      if (typeof value === 'string') {
        const weightMap: Record<string, number> = {
          'thin': 100,
          'extralight': 200,
          'light': 300,
          'normal': 400,
          'medium': 500,
          'semibold': 600,
          'bold': 700,
          'extrabold': 800,
          'black': 900
        }
        return weightMap[value.toLowerCase()] || value
      }
      return value
    }
    
    // 对于阴影，格式化为 CSS 阴影值
    if (token.$type === 'shadow') {
      if (typeof value === 'object') {
        return this.formatShadowValue(value)
      }
      return value
    }
    
    return value
  }

  private formatShadowValue(shadow: any): string {
    if (typeof shadow === 'string') {
      return shadow
    }
    
    const { offsetX = 0, offsetY = 0, blur = 0, spread = 0, color = '#000000' } = shadow
    return `${offsetX}px ${offsetY}px ${blur}px ${spread}px ${color}`
  }

  private isDesignToken(value: any): value is DesignToken {
    return typeof value === 'object' && value !== null && '$value' in value
  }

  private async ensureDirectoryExists(filePath: string) {
    const dir = path.dirname(filePath)
    try {
      await fs.access(dir)
    } catch {
      await fs.mkdir(dir, { recursive: true })
    }
  }
}
