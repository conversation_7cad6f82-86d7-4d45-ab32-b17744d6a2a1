import { TsOutputConfig, TokenSet, DesignToken } from '../types'
import { Logger } from '../utils/logger'
import path from 'path'
import fs from 'fs/promises'

export class TypeScriptGenerator {
  private config: TsOutputConfig

  constructor(
    config: TsOutputConfig | boolean,
    private rootDir: string,
    private logger: Logger
  ) {
    this.config = typeof config === 'boolean' 
      ? { path: './src/utils/tokens.ts', format: 'object', typeDeclaration: true }
      : { format: 'object', typeDeclaration: true, ...config }
  }

  async generate(tokens: TokenSet) {
    this.logger.debug('Generating TypeScript output')
    
    const outputPath = path.resolve(this.rootDir, this.config.path)
    
    if (this.config.format === 'constants') {
      await this.generateConstants(tokens, outputPath)
    } else {
      await this.generateObject(tokens, outputPath)
    }
    
    // 生成类型声明文件
    if (this.config.typeDeclaration) {
      await this.generateTypeDeclaration(tokens, outputPath)
    }
    
    this.logger.info(`TypeScript tokens generated: ${outputPath}`)
  }

  private async generateObject(tokens: TokenSet, outputPath: string) {
    const content = this.generateObjectContent(tokens)
    
    await this.ensureDirectoryExists(outputPath)
    await fs.writeFile(outputPath, content, 'utf-8')
  }

  private async generateConstants(tokens: TokenSet, outputPath: string) {
    const content = this.generateConstantsContent(tokens)
    
    await this.ensureDirectoryExists(outputPath)
    await fs.writeFile(outputPath, content, 'utf-8')
  }

  private generateObjectContent(tokens: TokenSet): string {
    const tokenObject = this.convertToJavaScriptObject(tokens)
    
    return `// This file is auto-generated. Do not edit manually.
export const Tokens = ${JSON.stringify(tokenObject, null, 2)} as const;

export type TokensType = typeof Tokens;
`
  }

  private generateConstantsContent(tokens: TokenSet): string {
    let content = '// This file is auto-generated. Do not edit manually.\n\n'
    
    const constants = this.extractConstants(tokens)
    
    for (const [name, value] of Object.entries(constants)) {
      const constantName = this.createConstantName(name)
      content += `export const ${constantName} = ${JSON.stringify(value)};\n`
    }
    
    // 生成常量类型
    content += '\n// Token constants type\n'
    content += 'export interface TokenConstants {\n'
    
    for (const [name] of Object.entries(constants)) {
      const constantName = this.createConstantName(name)
      content += `  readonly ${constantName}: typeof ${constantName};\n`
    }
    
    content += '}\n'
    
    return content
  }

  private async generateTypeDeclaration(tokens: TokenSet, outputPath: string) {
    const typeContent = this.generateTypeContent(tokens)
    const typeFilePath = outputPath.replace(/\.ts$/, '.d.ts')
    
    await this.ensureDirectoryExists(typeFilePath)
    await fs.writeFile(typeFilePath, typeContent, 'utf-8')
  }

  private generateTypeContent(tokens: TokenSet): string {
    const typeDefinition = this.generateTypeDefinition(tokens, 'DesignTokens')
    
    return `// This file is auto-generated. Do not edit manually.
${typeDefinition}

export declare const Tokens: DesignTokens;
export type TokensType = DesignTokens;
`
  }

  private generateTypeDefinition(tokens: TokenSet, typeName: string, indent = 0): string {
    const indentStr = '  '.repeat(indent)
    let content = `${indentStr}export interface ${typeName} {\n`
    
    for (const [key, value] of Object.entries(tokens)) {
      const propertyName = this.sanitizePropertyName(key)
      
      if (this.isDesignToken(value)) {
        const tokenValue = this.formatTypeScriptValue(value.$value)
        content += `${indentStr}  readonly ${propertyName}: ${tokenValue};\n`
      } else if (typeof value === 'object' && value !== null) {
        const nestedTypeName = this.createNestedTypeName(key)
        content += `${indentStr}  readonly ${propertyName}: ${nestedTypeName};\n`
      }
    }
    
    content += `${indentStr}}\n`
    
    // 生成嵌套类型
    for (const [key, value] of Object.entries(tokens)) {
      if (typeof value === 'object' && value !== null && !this.isDesignToken(value)) {
        const nestedTypeName = this.createNestedTypeName(key)
        content += '\n' + this.generateTypeDefinition(value as TokenSet, nestedTypeName, indent)
      }
    }
    
    return content
  }

  private convertToJavaScriptObject(tokens: TokenSet): any {
    const result: any = {}
    
    for (const [key, value] of Object.entries(tokens)) {
      if (this.isDesignToken(value)) {
        result[key] = value.$value
      } else if (typeof value === 'object' && value !== null) {
        result[key] = this.convertToJavaScriptObject(value as TokenSet)
      } else {
        result[key] = value
      }
    }
    
    return result
  }

  private extractConstants(tokens: TokenSet, prefix = ''): Record<string, any> {
    const constants: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(tokens)) {
      const fullKey = prefix ? `${prefix}.${key}` : key
      
      if (this.isDesignToken(value)) {
        constants[fullKey] = value.$value
      } else if (typeof value === 'object' && value !== null) {
        Object.assign(constants, this.extractConstants(value as TokenSet, fullKey))
      }
    }
    
    return constants
  }

  private createConstantName(path: string): string {
    return path
      .split('.')
      .map(part => part.toUpperCase().replace(/[^A-Z0-9]/g, '_'))
      .join('_')
  }

  private createNestedTypeName(key: string): string {
    return key.charAt(0).toUpperCase() + key.slice(1).replace(/[^a-zA-Z0-9]/g, '')
  }

  private sanitizePropertyName(name: string): string {
    // 如果属性名包含特殊字符，用引号包围
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(name)) {
      return name
    }
    return `"${name}"`
  }

  private formatTypeScriptValue(value: any): string {
    if (typeof value === 'string') {
      return `"${value}"`
    }
    
    if (typeof value === 'number' || typeof value === 'boolean') {
      return value.toString()
    }
    
    if (Array.isArray(value)) {
      return `[${value.map(v => this.formatTypeScriptValue(v)).join(', ')}]`
    }
    
    if (typeof value === 'object' && value !== null) {
      const props = Object.entries(value)
        .map(([k, v]) => `${this.sanitizePropertyName(k)}: ${this.formatTypeScriptValue(v)}`)
        .join('; ')
      return `{ ${props} }`
    }
    
    return 'any'
  }

  private isDesignToken(value: any): value is DesignToken {
    return typeof value === 'object' && value !== null && '$value' in value
  }

  private async ensureDirectoryExists(filePath: string) {
    const dir = path.dirname(filePath)
    try {
      await fs.access(dir)
    } catch {
      await fs.mkdir(dir, { recursive: true })
    }
  }
}
