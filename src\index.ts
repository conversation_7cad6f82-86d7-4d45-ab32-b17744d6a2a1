import type { Plugin } from 'vite'
import { DesignTokensPluginOptions } from './types'
import { DesignTokensCore } from './core'
import { createLogger } from './utils/logger'

export * from './types'

export default function designTokensPlugin(options: DesignTokensPluginOptions = {}): Plugin {
  const logger = createLogger(options.debug)
  let core: DesignTokensCore

  return {
    name: 'vite-plugin-design-tokens',
    
    configResolved(config) {
      core = new DesignTokensCore(options, config, logger)
    },

    async buildStart() {
      logger.info('Design tokens plugin started')
      await core.initialize()
      await core.generateTokens()
    },

    async configureServer(server) {
      if (options.watch !== false) {
        await core.startWatching(server)
      }
      
      // 添加中间件处理文档页面
      if (options.documentation) {
        server.middlewares.use('/__design-tokens', (req, res, next) => {
          core.handleDocumentationRequest(req, res, next)
        })
      }
    },

    async handleHotUpdate(ctx) {
      const shouldReload = await core.handleFileChange(ctx.file)
      if (shouldReload) {
        logger.info('Design tokens updated, triggering reload')
        ctx.server.ws.send({
          type: 'full-reload'
        })
        return []
      }
    },

    async buildEnd() {
      await core.cleanup()
    }
  }
}
