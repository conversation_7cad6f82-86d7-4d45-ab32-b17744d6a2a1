import { TokenSet, DesignToken, Transformer, TransformContext } from '../types'
import { Logger } from '../utils/logger'

export class TokenTransformer {
  constructor(
    private customTransformers: Transformer[],
    private logger: Logger
  ) {}

  transform(tokens: TokenSet, context: TransformContext): TokenSet {
    this.logger.debug('Transforming design tokens')
    
    // 应用内置转换器
    let transformedTokens = this.applyBuiltinTransformers(tokens, context)
    
    // 应用自定义转换器
    transformedTokens = this.applyCustomTransformers(transformedTokens, context)
    
    return transformedTokens
  }

  private applyBuiltinTransformers(tokens: TokenSet, context: TransformContext): TokenSet {
    return this.walkTokens(tokens, (token) => {
      // 标准化颜色值
      if (token.$type === 'color') {
        token = this.normalizeColorToken(token)
      }
      
      // 标准化尺寸值
      if (token.$type === 'dimension') {
        token = this.normalizeDimensionToken(token)
      }
      
      // 标准化字体权重
      if (token.$type === 'fontWeight') {
        token = this.normalizeFontWeightToken(token)
      }
      
      return token
    })
  }

  private applyCustomTransformers(tokens: TokenSet, context: TransformContext): TokenSet {
    let result = tokens
    
    for (const transformer of this.customTransformers) {
      this.logger.debug(`Applying transformer: ${transformer.name}`)
      result = this.walkTokens(result, (token) => {
        try {
          return transformer.transform(token, context)
        } catch (error) {
          this.logger.error(`Error in transformer ${transformer.name}:`, error)
          return token
        }
      })
    }
    
    return result
  }

  private walkTokens(tokens: TokenSet, transform: (token: DesignToken) => DesignToken): TokenSet {
    const result: TokenSet = {}
    
    for (const [key, value] of Object.entries(tokens)) {
      if (this.isDesignToken(value)) {
        result[key] = transform(value)
      } else if (typeof value === 'object' && value !== null) {
        result[key] = this.walkTokens(value as TokenSet, transform)
      } else {
        result[key] = value
      }
    }
    
    return result
  }

  private isDesignToken(value: any): value is DesignToken {
    return typeof value === 'object' && value !== null && '$value' in value
  }

  private normalizeColorToken(token: DesignToken): DesignToken {
    const value = token.$value
    
    if (typeof value === 'string') {
      // 标准化十六进制颜色
      if (value.startsWith('#')) {
        const normalized = this.normalizeHexColor(value)
        return { ...token, $value: normalized }
      }
      
      // 标准化 RGB/RGBA 颜色
      if (value.startsWith('rgb')) {
        const normalized = this.normalizeRgbColor(value)
        return { ...token, $value: normalized }
      }
    }
    
    return token
  }

  private normalizeDimensionToken(token: DesignToken): DesignToken {
    const value = token.$value
    
    if (typeof value === 'string') {
      // 确保数值有单位
      if (/^\d+$/.test(value)) {
        return { ...token, $value: `${value}px` }
      }
      
      // 标准化单位
      const normalized = value.replace(/\s+/g, '')
      return { ...token, $value: normalized }
    }
    
    if (typeof value === 'number') {
      return { ...token, $value: `${value}px` }
    }
    
    return token
  }

  private normalizeFontWeightToken(token: DesignToken): DesignToken {
    const value = token.$value
    
    if (typeof value === 'string') {
      // 转换命名权重为数值
      const weightMap: Record<string, number> = {
        'thin': 100,
        'extra-light': 200,
        'light': 300,
        'normal': 400,
        'medium': 500,
        'semi-bold': 600,
        'bold': 700,
        'extra-bold': 800,
        'black': 900
      }
      
      const normalizedKey = value.toLowerCase().replace(/\s+/g, '-')
      if (weightMap[normalizedKey]) {
        return { ...token, $value: weightMap[normalizedKey] }
      }
    }
    
    return token
  }

  private normalizeHexColor(hex: string): string {
    // 移除 # 符号
    let color = hex.replace('#', '')
    
    // 转换 3 位十六进制为 6 位
    if (color.length === 3) {
      color = color.split('').map(c => c + c).join('')
    }
    
    // 确保是有效的十六进制颜色
    if (!/^[0-9A-Fa-f]{6}$/.test(color)) {
      this.logger.warn(`Invalid hex color: ${hex}`)
      return hex
    }
    
    return `#${color.toLowerCase()}`
  }

  private normalizeRgbColor(rgb: string): string {
    // 标准化 RGB/RGBA 格式
    const normalized = rgb
      .replace(/\s+/g, '')
      .replace(/rgba?\(/, 'rgb(')
    
    return normalized
  }
}
