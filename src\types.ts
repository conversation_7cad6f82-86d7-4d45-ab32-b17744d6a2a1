import type { Plugin } from 'vite'

// 设计令牌基础类型
export interface DesignToken {
  $value: string | number | boolean | object
  $type?: 'color' | 'dimension' | 'fontFamily' | 'fontWeight' | 'duration' | 'cubicBezier' | 'number' | 'strokeStyle' | 'border' | 'transition' | 'shadow' | 'gradient' | 'typography'
  $description?: string
  $extensions?: Record<string, any>
}

export interface TokenSet {
  [key: string]: DesignToken | TokenSet
}

// Figma 配置
export interface FigmaConfig {
  fileId: string
  token: string
  page?: string
  pollingInterval?: number
  headers?: Record<string, string>
}

// JSON 文件配置
export interface JsonConfig {
  path: string
  watch?: boolean
}

// 输出配置
export interface CssOutputConfig {
  path: string
  selector?: string
  prefix?: string
  darkThemeSelector?: string
}

export interface TsOutputConfig {
  path: string
  format?: 'object' | 'constants'
  typeDeclaration?: boolean
}

export interface TailwindOutputConfig {
  path: string
  extend?: boolean
}

export interface JsonOutputConfig {
  path: string
  minify?: boolean
}

export interface DocumentationConfig {
  path: string
  title?: string
  open?: boolean
}

export interface OutputConfig {
  css?: CssOutputConfig | boolean
  ts?: TsOutputConfig | boolean
  tailwind?: TailwindOutputConfig | boolean
  json?: JsonOutputConfig | boolean
}

// 转换器
export interface TransformContext {
  mode: 'build' | 'serve'
  root: string
  outputFormats: string[]
  tokenSet: TokenSet
}

export interface Transformer {
  name: string
  transform: (token: DesignToken, context: TransformContext) => DesignToken
}

// 插件配置
export interface DesignTokensPluginOptions {
  figma?: FigmaConfig
  json?: JsonConfig | string
  output?: OutputConfig
  transformers?: Transformer[]
  watch?: boolean
  debug?: boolean
  documentation?: DocumentationConfig | boolean
}

// Figma API 响应类型
export interface FigmaFile {
  document: FigmaNode
  components: Record<string, FigmaComponent>
  styles: Record<string, FigmaStyle>
}

export interface FigmaNode {
  id: string
  name: string
  type: string
  children?: FigmaNode[]
  fills?: FigmaPaint[]
  strokes?: FigmaPaint[]
  effects?: FigmaEffect[]
  style?: FigmaTypeStyle
}

export interface FigmaComponent {
  key: string
  name: string
  description: string
}

export interface FigmaStyle {
  key: string
  name: string
  description: string
  styleType: 'FILL' | 'TEXT' | 'EFFECT' | 'GRID'
}

export interface FigmaPaint {
  type: 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL' | 'GRADIENT_ANGULAR' | 'GRADIENT_DIAMOND' | 'IMAGE'
  color?: FigmaColor
  gradientStops?: FigmaColorStop[]
}

export interface FigmaColor {
  r: number
  g: number
  b: number
  a: number
}

export interface FigmaColorStop {
  position: number
  color: FigmaColor
}

export interface FigmaEffect {
  type: 'DROP_SHADOW' | 'INNER_SHADOW' | 'LAYER_BLUR' | 'BACKGROUND_BLUR'
  color?: FigmaColor
  offset?: { x: number; y: number }
  radius: number
  spread?: number
}

export interface FigmaTypeStyle {
  fontFamily: string
  fontPostScriptName: string
  fontWeight: number
  fontSize: number
  lineHeightPx: number
  letterSpacing: number
}

export type DesignTokensPlugin = (options?: DesignTokensPluginOptions) => Plugin
