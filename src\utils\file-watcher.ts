import chokidar from 'chokidar'
import { EventEmitter } from 'events'
import { Logger } from './logger'

export class FileWatcher extends EventEmitter {
  private watcher: chokidar.FSWatcher

  constructor(
    private paths: string[],
    private logger: Logger
  ) {
    super()
    
    this.watcher = chokidar.watch(paths, {
      persistent: true,
      ignoreInitial: true,
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 100
      }
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.watcher.on('change', (filePath) => {
      this.logger.debug(`File changed: ${filePath}`)
      this.emit('change', filePath)
    })

    this.watcher.on('add', (filePath) => {
      this.logger.debug(`File added: ${filePath}`)
      this.emit('change', filePath)
    })

    this.watcher.on('unlink', (filePath) => {
      this.logger.debug(`File removed: ${filePath}`)
      this.emit('change', filePath)
    })

    this.watcher.on('error', (error) => {
      this.logger.error('File watcher error:', error)
      this.emit('error', error)
    })
  }

  close() {
    this.watcher.close()
    this.removeAllListeners()
  }
}
