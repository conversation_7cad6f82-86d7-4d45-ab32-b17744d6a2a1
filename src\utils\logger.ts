import pc from 'picocolors'

export interface Logger {
  debug(message: string, ...args: any[]): void
  info(message: string, ...args: any[]): void
  warn(message: string, ...args: any[]): void
  error(message: string, ...args: any[]): void
}

export function createLogger(debug = false): Logger {
  const prefix = pc.cyan('[design-tokens]')
  
  return {
    debug(message: string, ...args: any[]) {
      if (debug) {
        console.log(pc.gray(`${prefix} ${message}`), ...args)
      }
    },
    
    info(message: string, ...args: any[]) {
      console.log(`${prefix} ${pc.green(message)}`, ...args)
    },
    
    warn(message: string, ...args: any[]) {
      console.warn(`${prefix} ${pc.yellow(message)}`, ...args)
    },
    
    error(message: string, ...args: any[]) {
      console.error(`${prefix} ${pc.red(message)}`, ...args)
    }
  }
}
