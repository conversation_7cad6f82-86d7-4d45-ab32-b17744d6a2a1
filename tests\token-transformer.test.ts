import { describe, it, expect } from 'vitest'
import { TokenTransformer } from '../src/transformers/token-transformer'
import { createLogger } from '../src/utils/logger'
import { TokenSet, TransformContext } from '../src/types'

describe('TokenTransformer', () => {
  const logger = createLogger(false)
  const context: TransformContext = {
    mode: 'build',
    root: '/test',
    outputFormats: ['css', 'ts'],
    tokenSet: {}
  }

  it('should normalize hex colors', () => {
    const transformer = new TokenTransformer([], logger)
    const tokens: TokenSet = {
      color: {
        primary: {
          $value: '#f00',
          $type: 'color'
        }
      }
    }

    const result = transformer.transform(tokens, context)
    expect((result.color as any).primary.$value).toBe('#ff0000')
  })

  it('should normalize dimensions', () => {
    const transformer = new TokenTransformer([], logger)
    const tokens: TokenSet = {
      spacing: {
        small: {
          $value: '8',
          $type: 'dimension'
        },
        medium: {
          $value: 16,
          $type: 'dimension'
        }
      }
    }

    const result = transformer.transform(tokens, context)
    expect((result.spacing as any).small.$value).toBe('8px')
    expect((result.spacing as any).medium.$value).toBe('16px')
  })

  it('should apply custom transformers', () => {
    const customTransformer = {
      name: 'test-transformer',
      transform: (token: any) => {
        if (token.$type === 'color') {
          return {
            ...token,
            $value: token.$value.toUpperCase()
          }
        }
        return token
      }
    }

    const transformer = new TokenTransformer([customTransformer], logger)
    const tokens: TokenSet = {
      color: {
        primary: {
          $value: '#ff0000',
          $type: 'color'
        }
      }
    }

    const result = transformer.transform(tokens, context)
    expect((result.color as any).primary.$value).toBe('#FF0000')
  })

  it('should handle nested token structures', () => {
    const transformer = new TokenTransformer([], logger)
    const tokens: TokenSet = {
      color: {
        primary: {
          light: {
            $value: '#3399ff',
            $type: 'color'
          },
          dark: {
            $value: '#0044cc',
            $type: 'color'
          }
        }
      }
    }

    const result = transformer.transform(tokens, context)
    expect((result.color as any).primary.light.$value).toBe('#3399ff')
    expect((result.color as any).primary.dark.$value).toBe('#0044cc')
  })
})
