{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "outDir": "dist", "rootDir": "src", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "noEmit": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}